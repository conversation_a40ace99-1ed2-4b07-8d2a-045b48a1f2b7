import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { SettingsProvider, useSettings } from './contexts/SettingsContext';
import Header from './components/Header';
import PromptSection from './components/PromptSection';
import EnhancedMusicCanvas from './components/EnhancedMusicCanvas';

import HintSuggestions from './components/HintSuggestions';
import Sidebar from './components/Sidebar';

import HistoryPanel from './components/HistoryPanel';
import SettingsPanel from './components/SettingsPanel';

import SearchPanel from './components/SearchPanel';
import DownloadsPanel from './components/DownloadsPanel';
import SharePanel from './components/SharePanel';
import MusicBackground from './components/MusicBackground';
import './styles/globals.css';

const AppContent: React.FC = () => {
  const { settings } = useSettings();
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [showHints, setShowHints] = useState(false);
  const [isSidebarExpanded, setIsSidebarExpanded] = useState(false);

  // Panel states

  const [showHistory, setShowHistory] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const [showSearch, setShowSearch] = useState(false);
  const [showDownloads, setShowDownloads] = useState(false);
  const [showShare, setShowShare] = useState(false);

  // Current project for sharing
  const [currentProject, setCurrentProject] = useState<any>(null);

  const handleGenerate = async () => {
    if (!prompt.trim()) return;

    setIsGenerating(true);
    setShowHints(false);

    // Simulate AI generation
    await new Promise(resolve => setTimeout(resolve, 3000));

    setIsGenerating(false);
    setHasGenerated(true);
    setShowAdvanced(true);
    setShowHints(true);

    // Create new project
    const newProject = {
      id: Date.now().toString(),
      name: `Generated Track ${Date.now()}`,
      prompt: prompt,
      audioUrl: "/beat-freestyle.mp3",
      createdAt: new Date(),
      duration: settings.defaultDuration, // Use setting
      style: "Lo-fi", // Default style, should be taken from settings
      bpm: 128,
      isFavorite: false
    };

    // Set current project for sharing
    setCurrentProject(newProject);



    // Save to history
    const historyItem = {
      id: Date.now().toString(),
      prompt: prompt,
      timestamp: new Date(),
      duration: 180,
      style: "Lo-fi",
      settings: {
        bpm: 128,
        instruments: ['piano', 'bass', 'drums']
      }
    };

    const existingHistory = JSON.parse(localStorage.getItem('promptbeat-history') || '[]');
    const updatedHistory = [historyItem, ...existingHistory.slice(0, 49)]; // Keep only 50 items
    localStorage.setItem('promptbeat-history', JSON.stringify(updatedHistory));
  };

  const handleNewProject = () => {
    setPrompt('');
    setHasGenerated(false);
    setShowAdvanced(false);
    setShowHints(false);
    setIsGenerating(false);
  };

  // Helper function to close all panels
  const closeAllPanels = () => {
    setShowHistory(false);
    setShowSettings(false);
    setShowSearch(false);
    setShowDownloads(false);
    setShowShare(false);
  };

  const handleOpenHistory = () => {
    closeAllPanels();
    setShowHistory(true);
  };

  const handleOpenSettings = () => {
    closeAllPanels();
    setShowSettings(true);
  };



  const handleOpenSearch = () => {
    closeAllPanels();
    setShowSearch(true);
  };

  const handleOpenDownloads = () => {
    closeAllPanels();
    setShowDownloads(true);
  };

  const handleOpenShare = () => {
    closeAllPanels();
    setShowShare(true);
  };



  const handleRestoreFromHistory = (historyItem: any) => {
    setPrompt(historyItem.prompt);
    setHasGenerated(true);
    setShowAdvanced(true);
    setShowHistory(false);
  };

  const handleClearPrompt = () => {
    setPrompt('');
    setHasGenerated(false);
    setShowAdvanced(false);
    setShowHints(false);
  };

  const handleRandomPrompt = () => {
    // Random prompt is handled in PromptSection
    setShowHints(true);
  };



  const handleRedownload = (item: any) => {
    // Simulate redownload
    console.log('Redownloading:', item);
  };

  const handleAddToFavorites = () => {
    if (!hasGenerated) return;

    const favoriteItem = {
      id: Date.now().toString(),
      name: "Beat for Freestyle",
      prompt: prompt,
      createdAt: new Date(),
      addedToFavoritesAt: new Date(),
      duration: 180, // 3 minutes
      style: "Freestyle",
      tags: ["beat", "freestyle", "hip-hop"]
    };

    const favorites = JSON.parse(localStorage.getItem('promptbeat-favorites') || '[]');
    const isAlreadyFavorite = favorites.some((fav: any) => fav.prompt === prompt);

    if (!isAlreadyFavorite) {
      favorites.unshift(favoriteItem);
      localStorage.setItem('promptbeat-favorites', JSON.stringify(favorites.slice(0, 50))); // Keep only 50 items
    }
  };

  const isCurrentTrackFavorite = () => {
    if (!hasGenerated) return false;
    const favorites = JSON.parse(localStorage.getItem('promptbeat-favorites') || '[]');
    return favorites.some((fav: any) => fav.prompt === prompt);
  };



  return (
    <div className="bg-black relative overflow-x-hidden min-h-screen">
      {/* Sidebar */}
      <Sidebar
        onNewProject={handleNewProject}
        onOpenHistory={handleOpenHistory}
        onOpenSettings={handleOpenSettings}


        onOpenSearch={handleOpenSearch}
        onOpenDownloads={handleOpenDownloads}
        onOpenShare={handleOpenShare}
        onSidebarStateChange={setIsSidebarExpanded}
      />

      {/* Music-themed animated background */}
      <MusicBackground
        isGenerating={isGenerating}
        intensity={hasGenerated ? 'high' : 'medium'}
      />

      <div className="relative z-10">
        {/* Header - always visible but logo hidden when sidebar expanded */}
        <div
          className="relative z-20 transition-all duration-300 ease-in-out"
          style={{
            marginLeft: isSidebarExpanded ? '280px' : '80px'
          }}
        >
          <Header hideLogo={isSidebarExpanded} />
        </div>

        <main
          className="pb-20 transition-all duration-300 ease-in-out relative z-20"
          style={{
            marginLeft: isSidebarExpanded ? '280px' : '80px',
            width: `calc(100vw - ${isSidebarExpanded ? '280px' : '80px'})`,
            minHeight: '100vh',
            overflow: 'visible'
          }}
        >
          <div className="max-w-7xl mx-auto px-6">
            <PromptSection
              prompt={prompt}
              setPrompt={setPrompt}
              onGenerate={handleGenerate}
              isGenerating={isGenerating}
              hasGenerated={hasGenerated}
              showAdvanced={showAdvanced}
              onToggleAdvanced={() => setShowAdvanced(!showAdvanced)}
              onClear={handleClearPrompt}
              onRandom={handleRandomPrompt}
            />

            <AnimatePresence>
              {hasGenerated && (
                <motion.div
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -50 }}
                  transition={{ duration: 0.6, ease: "easeOut" }}
                >
                  <EnhancedMusicCanvas
                    isGenerating={isGenerating}
                    audioSrc="/beat-freestyle.mp3"
                    songDataSrc="/beat-freestyle.json"
                    title="Beat for Freestyle"
                    artist="PromptBeat AI"
                    bpm={128}
                    onShare={handleOpenShare}
                    onDownload={(format, quality) => {
                      console.log(`Downloading in ${format} format at ${quality} quality`);
                    }}
                    onAddToFavorites={handleAddToFavorites}
                    isFavorite={isCurrentTrackFavorite()}
                  />
                </motion.div>
              )}
            </AnimatePresence>

            <AnimatePresence>
              {showHints && settings.showHints && (
                <HintSuggestions onHintClick={(hint) => setPrompt(prev => prev + ' ' + hint)} />
              )}
            </AnimatePresence>
          </div>
        </main>
      </div>

      {/* Modals */}
      <AnimatePresence>

      </AnimatePresence>

      <AnimatePresence>
        {showHistory && (
          <HistoryPanel
            isOpen={showHistory}
            onClose={() => setShowHistory(false)}
            onRestoreFromHistory={handleRestoreFromHistory}
          />
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showSettings && (
          <SettingsPanel
            isOpen={showSettings}
            onClose={() => setShowSettings(false)}
          />
        )}
      </AnimatePresence>



      <AnimatePresence>
        {showSearch && (
          <SearchPanel
            isOpen={showSearch}
            onClose={() => setShowSearch(false)}

            onOpenHistory={handleOpenHistory}
            onOpenSettings={handleOpenSettings}

          />
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showDownloads && (
          <DownloadsPanel
            isOpen={showDownloads}
            onClose={() => setShowDownloads(false)}
            onRedownload={handleRedownload}

          />
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showShare && (
          <SharePanel
            isOpen={showShare}
            onClose={() => setShowShare(false)}
            currentProject={currentProject}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

function App() {
  return (
    <SettingsProvider>
      <AppContent />
    </SettingsProvider>
  );
}

export default App;