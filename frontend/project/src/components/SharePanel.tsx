import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Share2, 
  Copy, 
  Mail, 
  MessageCircle,
  Facebook,
  Twitter,
  Instagram,
  Link,
  QrCode,
  Download,
  CheckCircle,
  ExternalLink
} from 'lucide-react';

interface ShareItem {
  id: string;
  projectId: string;
  projectName: string;
  shareUrl: string;
  shareType: 'link' | 'social' | 'email' | 'qr';
  platform?: string;
  createdAt: Date;
  clicks: number;
  isActive: boolean;
}

interface SharePanelProps {
  isOpen: boolean;
  onClose: () => void;
  currentProject?: {
    id: string;
    name: string;
    prompt: string;
    audioUrl?: string;
  };
}

const SharePanel: React.FC<SharePanelProps> = ({
  isOpen,
  onClose,
  currentProject
}) => {
  const [shareHistory, setShareHistory] = useState<ShareItem[]>([]);
  const [copiedText, setCopiedText] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState<'share' | 'history'>('share');

  // Load share history from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('promptbeat-share-history');
    if (saved) {
      const parsed = JSON.parse(saved);
      setShareHistory(parsed.map((item: any) => ({
        ...item,
        createdAt: new Date(item.createdAt)
      })));
    }
  }, []);

  // Save share history to localStorage
  useEffect(() => {
    localStorage.setItem('promptbeat-share-history', JSON.stringify(shareHistory));
  }, [shareHistory]);

  const generateShareUrl = (projectId: string) => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/share/${projectId}`;
  };

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(label);
      setTimeout(() => setCopiedText(null), 2000);
      
      // Add to share history
      if (currentProject) {
        const newShare: ShareItem = {
          id: Date.now().toString(),
          projectId: currentProject.id,
          projectName: currentProject.name,
          shareUrl: text,
          shareType: 'link',
          createdAt: new Date(),
          clicks: 0,
          isActive: true
        };
        setShareHistory(prev => [newShare, ...prev.slice(0, 19)]); // Keep only 20 items
      }
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const shareToSocial = (platform: string) => {
    if (!currentProject) return;

    const shareUrl = generateShareUrl(currentProject.id);
    const text = `Check out this amazing AI-generated music: "${currentProject.name}"`;
    
    let url = '';
    switch (platform) {
      case 'twitter':
        url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(shareUrl)}`;
        break;
      case 'facebook':
        url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`;
        break;
      case 'whatsapp':
        url = `https://wa.me/?text=${encodeURIComponent(`${text} ${shareUrl}`)}`;
        break;
      case 'telegram':
        url = `https://t.me/share/url?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(text)}`;
        break;
    }

    if (url) {
      window.open(url, '_blank', 'width=600,height=400');
      
      // Add to share history
      const newShare: ShareItem = {
        id: Date.now().toString(),
        projectId: currentProject.id,
        projectName: currentProject.name,
        shareUrl: shareUrl,
        shareType: 'social',
        platform: platform,
        createdAt: new Date(),
        clicks: 0,
        isActive: true
      };
      setShareHistory(prev => [newShare, ...prev.slice(0, 19)]);
    }
  };

  const shareViaEmail = () => {
    if (!currentProject) return;

    const shareUrl = generateShareUrl(currentProject.id);
    const subject = `Check out this AI-generated music: ${currentProject.name}`;
    const body = `Hi!\n\nI wanted to share this amazing AI-generated music track with you:\n\n"${currentProject.name}"\n\nPrompt: ${currentProject.prompt}\n\nListen here: ${shareUrl}\n\nCreated with PromptBeat AI`;
    
    const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoUrl;

    // Add to share history
    const newShare: ShareItem = {
      id: Date.now().toString(),
      projectId: currentProject.id,
      projectName: currentProject.name,
      shareUrl: shareUrl,
      shareType: 'email',
      createdAt: new Date(),
      clicks: 0,
      isActive: true
    };
    setShareHistory(prev => [newShare, ...prev.slice(0, 19)]);
  };

  const generateQRCode = async () => {
    if (!currentProject) return;
    
    const shareUrl = generateShareUrl(currentProject.id);
    // In a real app, you'd use a QR code library here
    // For now, we'll just copy the URL and show a message
    await copyToClipboard(shareUrl, 'QR Code URL');
  };

  const deleteShareItem = (itemId: string) => {
    setShareHistory(prev => prev.filter(item => item.id !== itemId));
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  if (!isOpen) return null;

  const shareUrl = currentProject ? generateShareUrl(currentProject.id) : '';

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-black/90 backdrop-blur-xl border border-white/10 rounded-2xl w-full max-w-2xl max-h-[80vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="p-6 border-b border-white/10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-pink-500 to-purple-500 rounded-lg">
                <Share2 className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">Share Music</h2>
                <p className="text-white/60 text-sm">
                  {currentProject ? `Share "${currentProject.name}"` : 'Share your music with others'}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-white/60" />
            </button>
          </div>

          {/* Tabs */}
          <div className="mt-4 flex gap-2">
            <button
              onClick={() => setSelectedTab('share')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                selectedTab === 'share'
                  ? 'bg-pink-500/20 text-pink-300 border border-pink-500/30'
                  : 'bg-white/5 text-white/60 hover:bg-white/10'
              }`}
            >
              Share Options
            </button>
            <button
              onClick={() => setSelectedTab('history')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                selectedTab === 'history'
                  ? 'bg-pink-500/20 text-pink-300 border border-pink-500/30'
                  : 'bg-white/5 text-white/60 hover:bg-white/10'
              }`}
            >
              Share History ({shareHistory.length})
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {selectedTab === 'share' ? (
            <div className="space-y-6">
              {!currentProject ? (
                <div className="text-center py-8">
                  <Share2 className="w-12 h-12 text-white/20 mx-auto mb-4" />
                  <p className="text-white/60">No project selected to share</p>
                </div>
              ) : (
                <>
                  {/* Direct Link */}
                  <div>
                    <h3 className="text-white font-medium mb-3 flex items-center gap-2">
                      <Link className="w-4 h-4" />
                      Direct Link
                    </h3>
                    <div className="bg-white/5 border border-white/10 rounded-lg p-3">
                      <div className="flex items-center gap-2">
                        <input
                          type="text"
                          value={shareUrl}
                          readOnly
                          className="flex-1 bg-transparent text-white/80 text-sm focus:outline-none"
                        />
                        <button
                          onClick={() => copyToClipboard(shareUrl, 'Link')}
                          className="flex items-center gap-1 bg-white/10 hover:bg-white/20 text-white px-3 py-1 rounded transition-colors"
                        >
                          {copiedText === 'Link' ? (
                            <>
                              <CheckCircle className="w-4 h-4 text-green-400" />
                              Copied!
                            </>
                          ) : (
                            <>
                              <Copy className="w-4 h-4" />
                              Copy
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Social Media */}
                  <div>
                    <h3 className="text-white font-medium mb-3">Social Media</h3>
                    <div className="grid grid-cols-2 gap-3">
                      <button
                        onClick={() => shareToSocial('twitter')}
                        className="flex items-center gap-3 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-300 p-3 rounded-lg transition-colors"
                      >
                        <Twitter className="w-5 h-5" />
                        Twitter
                      </button>
                      <button
                        onClick={() => shareToSocial('facebook')}
                        className="flex items-center gap-3 bg-blue-600/20 hover:bg-blue-600/30 border border-blue-600/30 text-blue-400 p-3 rounded-lg transition-colors"
                      >
                        <Facebook className="w-5 h-5" />
                        Facebook
                      </button>
                      <button
                        onClick={() => shareToSocial('whatsapp')}
                        className="flex items-center gap-3 bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 text-green-300 p-3 rounded-lg transition-colors"
                      >
                        <MessageCircle className="w-5 h-5" />
                        WhatsApp
                      </button>
                      <button
                        onClick={() => shareToSocial('telegram')}
                        className="flex items-center gap-3 bg-cyan-500/20 hover:bg-cyan-500/30 border border-cyan-500/30 text-cyan-300 p-3 rounded-lg transition-colors"
                      >
                        <MessageCircle className="w-5 h-5" />
                        Telegram
                      </button>
                    </div>
                  </div>

                  {/* Email */}
                  <div>
                    <h3 className="text-white font-medium mb-3">Email</h3>
                    <button
                      onClick={shareViaEmail}
                      className="w-full flex items-center justify-center gap-3 bg-orange-500/20 hover:bg-orange-500/30 border border-orange-500/30 text-orange-300 p-3 rounded-lg transition-colors"
                    >
                      <Mail className="w-5 h-5" />
                      Share via Email
                    </button>
                  </div>

                  {/* QR Code */}
                  <div>
                    <h3 className="text-white font-medium mb-3">QR Code</h3>
                    <button
                      onClick={generateQRCode}
                      className="w-full flex items-center justify-center gap-3 bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/30 text-purple-300 p-3 rounded-lg transition-colors"
                    >
                      <QrCode className="w-5 h-5" />
                      Generate QR Code
                    </button>
                  </div>
                </>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {shareHistory.length === 0 ? (
                <div className="text-center py-8">
                  <Share2 className="w-12 h-12 text-white/20 mx-auto mb-4" />
                  <p className="text-white/60">No share history yet</p>
                </div>
              ) : (
                shareHistory.map((item) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-white/5 border border-white/10 rounded-lg p-4 hover:bg-white/10 transition-colors group"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="text-white font-medium truncate">{item.projectName}</h4>
                          <span className="text-xs text-white/40 bg-white/10 px-2 py-1 rounded capitalize">
                            {item.shareType} {item.platform && `• ${item.platform}`}
                          </span>
                        </div>
                        <p className="text-white/60 text-sm truncate">{item.shareUrl}</p>
                        <div className="flex items-center gap-4 text-xs text-white/40 mt-1">
                          <span>{formatDate(item.createdAt)}</span>
                          <span>{item.clicks} clicks</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        <button
                          onClick={() => copyToClipboard(item.shareUrl, `History-${item.id}`)}
                          className="p-2 text-white/60 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
                          title="Copy link"
                        >
                          {copiedText === `History-${item.id}` ? (
                            <CheckCircle className="w-4 h-4 text-green-400" />
                          ) : (
                            <Copy className="w-4 h-4" />
                          )}
                        </button>
                        <button
                          onClick={() => deleteShareItem(item.id)}
                          className="p-2 text-white/40 hover:text-red-400 hover:bg-red-400/20 rounded-lg transition-colors"
                          title="Delete"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        {selectedTab === 'share' && copiedText && (
          <div className="p-4 border-t border-white/10">
            <div className="flex items-center justify-center gap-2 text-green-400">
              <CheckCircle className="w-4 h-4" />
              <span className="text-sm">{copiedText} copied to clipboard!</span>
            </div>
          </div>
        )}
      </motion.div>
    </motion.div>
  );
};

export default SharePanel;
