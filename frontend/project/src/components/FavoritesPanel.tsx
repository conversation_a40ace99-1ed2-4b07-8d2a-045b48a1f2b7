import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Heart, 
  Music, 
  Clock, 
  Play, 
  Trash2, 
  Download,
  Share2,
  Search,
  Filter,
  Star
} from 'lucide-react';

interface FavoriteProject {
  id: string;
  name: string;
  prompt: string;
  createdAt: Date;
  addedToFavoritesAt: Date;
  duration: number;
  style: string;
  tags: string[];
}

interface FavoritesPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onRemoveFromFavorites: (projectId: string) => void;
}

const FavoritesPanel: React.FC<FavoritesPanelProps> = ({
  isOpen,
  onClose,
  onRemoveFromFavorites
}) => {
  const [favorites, setFavorites] = useState<FavoriteProject[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'style' | 'duration'>('date');
  const [filterByStyle, setFilterByStyle] = useState<string>('all');

  // Load favorites from localStorage on mount
  useEffect(() => {
    const savedFavorites = localStorage.getItem('promptbeat-favorites');
    if (savedFavorites) {
      const parsed = JSON.parse(savedFavorites);
      setFavorites(parsed.map((fav: any) => ({
        ...fav,
        createdAt: new Date(fav.createdAt),
        addedToFavoritesAt: new Date(fav.addedToFavoritesAt)
      })));
    }
  }, []);

  // Save favorites to localStorage whenever favorites change
  useEffect(() => {
    localStorage.setItem('promptbeat-favorites', JSON.stringify(favorites));
  }, [favorites]);

  const filteredAndSortedFavorites = favorites
    .filter(fav => {
      const matchesSearch = fav.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           fav.prompt.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           fav.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesStyle = filterByStyle === 'all' || fav.style === filterByStyle;
      return matchesSearch && matchesStyle;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'date':
          return b.addedToFavoritesAt.getTime() - a.addedToFavoritesAt.getTime();
        case 'style':
          return a.style.localeCompare(b.style);
        case 'duration':
          return b.duration - a.duration;
        default:
          return 0;
      }
    });

  const removeFromFavorites = (projectId: string) => {
    setFavorites(prev => prev.filter(fav => fav.id !== projectId));
    onRemoveFromFavorites(projectId);
  };

  const getUniqueStyles = () => {
    const styles = [...new Set(favorites.map(fav => fav.style))];
    return styles.sort();
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-black/90 backdrop-blur-xl border border-white/10 rounded-2xl w-full max-w-5xl max-h-[80vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="p-6 border-b border-white/10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-pink-500 to-purple-500 rounded-lg">
                <Heart className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">Favorite Projects</h2>
                <p className="text-white/60 text-sm">Your saved favorite music projects</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-white/60" />
            </button>
          </div>

          {/* Search and Filters */}
          <div className="mt-4 flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/40" />
              <input
                type="text"
                placeholder="Search favorites..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-lg pl-10 pr-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-pink-500/50"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="bg-white/5 border border-white/10 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-pink-500/50"
              >
                <option value="date">Sort by Date</option>
                <option value="name">Sort by Name</option>
                <option value="style">Sort by Style</option>
                <option value="duration">Sort by Duration</option>
              </select>
              <select
                value={filterByStyle}
                onChange={(e) => setFilterByStyle(e.target.value)}
                className="bg-white/5 border border-white/10 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-pink-500/50"
              >
                <option value="all">All Styles</option>
                {getUniqueStyles().map(style => (
                  <option key={style} value={style}>{style}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Favorites List */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {filteredAndSortedFavorites.length === 0 ? (
            <div className="text-center py-12">
              <Heart className="w-12 h-12 text-white/20 mx-auto mb-4" />
              <p className="text-white/60 mb-4">
                {favorites.length === 0 ? 'No favorite projects yet' : 'No favorites match your search'}
              </p>
              <p className="text-white/40 text-sm">
                Add projects to favorites by clicking the heart icon
              </p>
            </div>
          ) : (
            <div className="grid gap-4">
              {filteredAndSortedFavorites.map((favorite) => (
                <motion.div
                  key={favorite.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-gradient-to-r from-pink-500/10 to-purple-500/10 border border-pink-500/20 rounded-lg p-4 hover:from-pink-500/20 hover:to-purple-500/20 transition-all group"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <h3 className="text-white font-medium truncate">{favorite.name}</h3>
                        <span className="text-xs text-white/40 bg-white/10 px-2 py-1 rounded">
                          {favorite.style}
                        </span>
                      </div>
                      <p className="text-white/60 text-sm mb-3 line-clamp-2">{favorite.prompt}</p>
                      <div className="flex items-center gap-4 text-xs text-white/40 mb-2">
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          Added {formatDate(favorite.addedToFavoritesAt)}
                        </div>
                        <div className="flex items-center gap-1">
                          <Music className="w-3 h-3" />
                          {favorite.duration}s
                        </div>
                      </div>
                      {favorite.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {favorite.tags.map((tag, index) => (
                            <span
                              key={index}
                              className="text-xs bg-white/10 text-white/60 px-2 py-1 rounded"
                            >
                              #{tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center gap-2 ml-4">
                      <button
                        onClick={() => console.log('Play favorite:', favorite)}
                        className="p-2 text-white/60 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
                        title="Play favorite"
                      >
                        <Play className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => removeFromFavorites(favorite.id)}
                        className="p-2 text-pink-400 hover:text-pink-300 hover:bg-pink-400/20 rounded-lg transition-colors"
                        title="Remove from favorites"
                      >
                        <Heart className="w-4 h-4 fill-current" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-white/10">
          <p className="text-white/40 text-sm text-center">
            {filteredAndSortedFavorites.length} of {favorites.length} favorite projects
          </p>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default FavoritesPanel;
