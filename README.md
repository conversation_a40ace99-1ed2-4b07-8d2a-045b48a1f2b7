# 🎹 PromptBeat AI  
**<PERSON><PERSON> prompt → got<PERSON><PERSON> beat w kilka sekund.**  
Kolorowa wizualizacja utworu, intuicyjne suwaki i możliwość „forkowania” gotowych tracków bez otwierania DAW-a.

---

## ✨ D<PERSON><PERSON>ego warto?
1. **Zero progu wejścia** – wpisujesz jedno zdanie i przesuwasz suwak długości (10 s – 5 min).  
2. **Natychmiastowy efekt** – od klikni<PERSON>cia **Generuj** do odsłuchu.  
3. **Music Canvas** – waveform + mini-pianoroll pokazują, co gra i kiedy.  
4. **Pełna kontrola, jeśli chcesz** – rozwi<PERSON> panel, odkliknij instrument, zmień tempo lub dociśnij bas jednym.suwakiem.  
5. **Kreatywna społ<PERSON>z<PERSON>** – każdy utwór ma publiczny link i przycisk **Fork**; remiksuj cudze pomysły w sekundę.  

---

## 🚀 Jak to działa?  
| Krok | Co robisz | Co widzisz |  
|------|-----------|------------|  
| 1️⃣ | **Opisz muzykę** w jednym zdaniu | Duże pole tekstowe z przykładem w placeholderze |  
| 2️⃣ | **Ustaw długość** utworu suwakiem | Podgląd czasu (np. `2 min 30 s`) |  
| 3️⃣ | Kliknij **🎹 Generuj** | Po kilku s: odtwarzacz + Music Canvas (waveform, pianoroll, BPM) |  
| 4️⃣ | **Personalizuj** | Panel z już zaznaczonymi parametrami (styl, tempo, instrumenty, dynamika, atmosfera) – zmiany podświetlają się na Canvasie; klik **🔁 Aktualizuj** odtwarza nową wersję |  
| 5️⃣ | **Udostępnij lub Fork** | Play / Download / Copy Link / Tweet • Fork otwiera kreator z tymi samymi nastawami, gotowy do modyfikacji |  

> **Podpowiedzi AI** po każdym generowaniu podsuną 3 szybkie pomysły („Dodaj syntezator tła”, „Zwiększ perkusję”…). Wystarczy kliknąć, aby wstawić gotową frazę do mini-promptu.

---

## 🎛️ Panele sterowania

| Panel | Elementy | Szybka zmiana |  
|-------|----------|---------------|  
| **Quick Style** | kafelki gatunków (Lo-fi / Hip-hop / EDM / Cinematic / Classical / Podcast Bed) | 1 klik = podmiana stylu & BPM |  
| **Fine Tune** | suwaki *Bass · Drums · Melody*, tempo ±, atmosfera (Ciepło / Jasność) | widzisz wizualne odbicie zmian na Canvasie |  
| **Mini-Prompt** | krótkie doprecyzowanie („Wycisz werbel…”) | inkrementalna regeneracja audio |  

---

## 🖼️ Music Canvas – co pokazuje?  
* **Waveform** – gęstość i głośność beatu.  
* **Mini-Pianoroll** – kolor każdego instrumentu; widzisz, gdzie wchodzi piano, gdzie bas.  
* **Segmenty formy** – tło podzielone na sekcje (A, B, Bridge…).  
* **Kursor Live** – biegnąca biała linia.  
* **BPM tag** – zawsze w prawym górnym rogu.  
*(Klik dowolny kolor → solo/mute danego instrumentu).*

---

## 🏁 Podsumowanie
PromptBeat AI to najszybszy sposób, aby:  
* **Stworzyć** utwór jednym zdaniem i suwakiem.  
* **Zrozumieć** go wzrokiem dzięki Music Canvas.  
* **Poprawić** w sekundę kilkoma suwakami lub mikro-promptem.  
* **Podzielić się** linkiem i patrzeć, jak społeczność tworzy kolejne wersje.  

👉 **Wpisz swoją pierwszą frazę i usłysz, jak brzmi!**  
